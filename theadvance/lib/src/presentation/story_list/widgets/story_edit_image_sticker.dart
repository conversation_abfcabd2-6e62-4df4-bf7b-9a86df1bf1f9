import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:dotted_border/dotted_border.dart';
import 'package:http/http.dart' as http;
import 'package:animations/animations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:custom_image_crop/custom_image_crop.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';
import 'package:glass/glass.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pro_image_editor/pro_image_editor.dart';

import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/nd_progresshud/loading_widget.dart';
import '../../../core/params/sticker_request_params.dart';
import '../../../core/params/sticker_set_request_params.dart';
import '../../../core/utils/mappers.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../domain/entities/social_upload_file.dart';
import '../../../domain/entities/sticker_set.dart';
import '../../../domain/entities/sticker_set_created_success.dart';
import '../../../domain/entities/sticker_social.dart';
import '../../../injector/injector.dart';
import '../../account/widgets/account_field.dart';
import '../../widgets/value_notifier_list.dart';
import '../bloc/sticker_bloc.dart';
import 'gallery.dart';
import 'story_snapple.dart';
import 'story_write_media_card.dart';

class StickerSave {
  StickerSave(this.params, this.type);

  final StickerRequestParams params;
  final StikerTypePop type;
}

enum StikerTypePop { selected, saveToLike, saveToSet }

class StoryEditImageSticker extends StatefulWidget {
  const StoryEditImageSticker({super.key, required this.setLayer});
  final Function(WidgetLayer) setLayer;

  @override
  State<StoryEditImageSticker> createState() => _StoryEditImageStickerState();
}

class _StoryEditImageStickerState extends State<StoryEditImageSticker>
    with TickerProviderStateMixin {
  StickerSetRequestParams params = const StickerSetRequestParams(page: 1);
  final ValueNotifierList<StickerSocialItems> sticker = ValueNotifierList([]);
  final ValueNotifierList<StickerSetSocialItems> sets = ValueNotifierList([]);
  final ValueNotifierList<String> stickersRecent = ValueNotifierList([]);

  final GlobalKey<SnappableState> _snappableKey = GlobalKey<SnappableState>();
  late TabController _tabController;
  late ScrollController _scrollController;

  List<String> tabs = [];
  List<GlobalKey> _sectionKeys = [];

  bool _isScrollingProgrammatically = false;
  @override
  void initState() {
    _tabController = TabController(length: 0, vsync: this);
    _scrollController = ScrollController();

    _scrollController.addListener(_handleScroll);
    _tabController.addListener(_handleTabSelection);

    WidgetsBinding.instance.addPostFrameCallback((final t) async {
      final stickersCache = await EZCache.shared.getStickerSocial();
      sets.setValue(getIt<Mapper>().convertList(stickersCache));

      final stickers = await EZCache.shared.getStickerSocialRecent();
      stickersRecent.setValue(stickers);
    });
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _handleScroll() {
    if (_isScrollingProgrammatically) {
      return;
    }

    final int activeTab = _getActiveTab();
    if (activeTab != _tabController.index) {
      // Sử dụng animateTo thay vì index để tránh conflict
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && activeTab != _tabController.index) {
          _tabController.animateTo(activeTab);
        }
      });
    }
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging && !_isScrollingProgrammatically) {
      _scrollToSection(_tabController.index);
    }
  }

  int _getActiveTab() {
    if (!_scrollController.hasClients || _sectionKeys.isEmpty) {
      return 0;
    }

    final double scrollOffset = _scrollController.offset;
    final double maxScrollExtent = _scrollController.position.maxScrollExtent;

    // Nếu đã scroll gần đến cuối, return tab cuối
    if (scrollOffset >= maxScrollExtent - 50) {
      return _sectionKeys.length - 1;
    }

    // Tìm section có top gần nhất với scrollOffset + một khoảng offset
    const double triggerOffset = 580;

    for (int i = _sectionKeys.length - 1; i >= 0; i--) {
      final RenderBox? renderBox =
          _sectionKeys[i].currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        // Tính vị trí của section trong scroll view
        final Offset position = renderBox.localToGlobal(Offset.zero);

        if (position.dy <= triggerOffset) {
          return i;
        }
      }
    }

    return 0;
  }

  void _scrollToSection(final int index) {
    if (_scrollController.hasClients && index < _sectionKeys.length) {
      // Xử lý đặc biệt cho tab cuối
      if (index == _sectionKeys.length - 1) {
        _isScrollingProgrammatically = true;
        _scrollController
            .animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            )
            .then((_) {
              _isScrollingProgrammatically = false;
            });
        return;
      }

      final RenderBox? renderBox =
          _sectionKeys[index].currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final double sectionTop =
            renderBox.localToGlobal(Offset.zero).dy + _scrollController.offset;

        // Điều chỉnh offset để section hiển thị tốt hơn
        double targetOffset = sectionTop; // Offset 20px từ top

        // Đảm bảo không scroll quá giới hạn
        targetOffset = targetOffset.clamp(
          0.0,
          _scrollController.position.maxScrollExtent,
        );

        _isScrollingProgrammatically = true;
        _scrollController
            .animateTo(
              targetOffset,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            )
            .then((_) {
              _isScrollingProgrammatically = false;
            });
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: BlocProvider(
        create: (final context) =>
            getIt<StickerBloc>()..add(StickerSetGet(params)),
        // ..add(StickerRecentGet()),
        child: BlocConsumer<StickerBloc, StickerState>(
          listener: (final context, final state) {
            if (state.status == StickerStatus.updateSetSuccess) {
              final StickerSetSocialItems data = Utils.getData(state.data);
              final idxSet = sets.value.indexWhere(
                (final e) => e.id == data.id,
              );
              if (idxSet != -1) {
                final getSet = sets.value[idxSet].copyWith(name: data.name);
                sets.updateValuebyIndex(idxSet, getSet);
              }
            }
            if (state.status == StickerStatus.removeSetSuccess) {
              final setId = state.data.toString();
              final indexW = sets.value.indexWhere((final e) => e.id == setId);
              sets.removeIndex(indexW);
            }
            if (state.status == StickerStatus.setSuccess) {
              final StickerSetSocial set = Utils.getData(state.data);

              final listSafe = set.items?.map((final e) {
                if (e.stickers?.isNotEmpty ?? false) {
                  e.stickers?.removeWhere(
                    (final w) => w.mimetype?.contains('video/') ?? false,
                  );
                }
                return e;
              }).toList();
              listSafe?.removeWhere((final e2) => e2.stickers?.isEmpty ?? true);
              EZCache.shared.saveStickerSocial(
                listSafe?.map((final e) => e.toJson()).toList() ?? [],
              );
              sets.setValue(listSafe);
              _tabController = TabController(
                length: listSafe?.length ?? 0,
                vsync: this,
              );
              tabs = listSafe?.map((final e) => e.name ?? '').toList() ?? [];
              setState(() {});
            }

            if (state.status == StickerStatus.updateSuccess) {
              final StickerSocialItems sticker = Utils.getData(state.data);
              if (state.type == StikerTypePop.selected) {
                final item = ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: SizedBox(
                    width: 60,
                    height: 60,
                    child: CachedNetworkImage(imageUrl: sticker.link ?? ''),
                  ),
                );
                final layer = WidgetLayer(widget: item);
                widget.setLayer(layer);
              }
            }
            if (state.status == StickerStatus.createSuccess) {
              final StickerSetCreatedSuccess data = Utils.getData(state.data);

              if (state.type == StikerTypePop.selected) {
              } else {
                final iSet = sets.value.indexWhere(
                  (final e) => e.id == data.set?.id,
                );
                if (iSet != -1) {
                  final item = sets.value[iSet];
                  item.stickers?.add(data.sticker ?? StickerSocialItems());
                  sets.updateValuebyIndex(iSet, item);
                } else {
                  final newSet = StickerSetSocialItems(
                    id: data.set?.id,
                    name: data.set?.name,
                    createdBy: data.set?.createdBy,
                    stickers: [data.sticker ?? StickerSocialItems()],
                  );
                  sets.add(newSet);
                }
              }
            }
            if (state.status == StickerStatus.uploadSuccess) {
              final SocialUploadFile att = Utils.getData(state.data);
              context.read<StickerBloc>().add(
                StickerCreate(
                  params:
                      state.params?.copyWith(url: att.link) ??
                      const StickerRequestParams(),
                  type: state.type,
                ),
              );
            }
          },
          buildWhen: (final previous, final current) {
            return current.status != StickerStatus.setSuccess;
          },
          builder: (final context, final state) {
            return CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverPersistentHeader(
                  floating: true,
                  delegate: _SliverAppBarDelegate(
                    maxHeight: 50,
                    minHeight: 50,
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ColoredBox(
                        color: Colors.white,
                        child: ValueListenableBuilder(
                          valueListenable: stickersRecent,
                          builder:
                              (
                                final context,
                                final vStickerRecent,
                                final child,
                              ) {
                                final setRecent = StickerSetSocialItems(
                                  id: 'recent',
                                  name: 'Recent',
                                  createdBy: '',
                                  stickers: vStickerRecent
                                      .map(
                                        (final e) => StickerSocialItems(
                                          id: e,
                                          link: e,
                                          mimetype: 'local',
                                        ),
                                      )
                                      .toList(),
                                );

                                return _buildSticker(
                                  context,
                                  widget.setLayer,
                                  setRecent,
                                );
                              },
                        ),
                      ),
                    ),
                  ),
                ),
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverAppBarDelegate(
                    maxHeight: 50,
                    minHeight: 50,
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ColoredBox(
                        color: Colors.white,
                        child: TabBar(
                          labelPadding: const EdgeInsets.symmetric(
                            horizontal: 4,
                          ),
                          isScrollable: true,
                          controller: _tabController,
                          // onTap: (final i) async {
                          //   tabbarIndex.value = i;
                          //   if (_tabController.index == 0) {
                          //     scrollController.jumpTo(0);
                          //   }
                          // },
                          tabs: tabs
                              .map((final tab) => Tab(text: tab))
                              .toList(),
                          indicatorColor: Colors.transparent,
                          labelStyle: Theme.of(context).textTheme.labelLarge,
                        ),
                      ),
                    ),
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: sets,
                  builder: (final context, final vSets, final child) {
                    tabs = vSets.map((final e) => e.name ?? '').toList();
                    _sectionKeys = List.generate(
                      vSets.map((final e) => e.name).toList().length,
                      (final index) => GlobalKey(),
                    );
                    return SliverList.builder(
                      itemCount: vSets.length,
                      itemBuilder: (final context, final i) {
                        final item = vSets[i];
                        final key = _sectionKeys[i];
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          key: key,
                          children: [
                            _buildItemSet(item, context, widget.setLayer),
                          ],
                        );
                      },
                    );
                  },
                ),
              ],
            );
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   children: [

            //   ],
            // );
          },
        ),
      ),
    );
  }

  ValueListenableBuilder<List<String>> _buildSticker(
    final BuildContext context,
    final Function(WidgetLayer) setLayer,
    final StickerSetSocialItems items,
  ) {
    return ValueListenableBuilder(
      valueListenable: stickersRecent,
      builder: (final context, final vSticker, final child) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              GestureDetector(
                onTap: () async {
                  final ValueNotifier<File?> imageFile = ValueNotifier(null);


                  final controller = CustomImageCropController();
                   showModalBottomSheet(
    context:  context,
    builder: (final context) {
  final ValueNotifier<File?> imageSelected = ValueNotifier(null);
  final ValueNotifierList<String> imgDisplaySelected = ValueNotifierList([]);
      return SizedBox(
        height: MediaQuery.sizeOf(context).height * .8,
        child: Scaffold(
          body: Column(
            mainAxisSize: MainAxisSize.min,
            children: [ 
     _buildHeaderMedia(
                context.l10n.gallery,
                action: ValueListenableBuilder(
                  valueListenable: imageSelected,
                  builder: (final context, final vImage, final child) {
                    return IconButton(
                      onPressed: vImage == null
                          ? null
                          : () async {
                           context.router.popForced();
                      if (context.mounted) {
                        imageFile.value = vImage;
                        _showDropSticker(context, imageFile, controller).then((
                          final val,
                        ) {
                          final isStickerType =
                              val != null && val is StickerSave;
                          if (isStickerType) {
                            if (context.mounted) {
                              if (val.type == StikerTypePop.selected) {
                                final baseByte = base64Encode(
                                  File(val.params.url ?? '').readAsBytesSync(),
                                );
                                stickersRecent.add(baseByte);
    
                                EZCache.shared.saveStickerSocialRecent(
                                  stickersRecent.value,
                                );
                                setLayer(
                                  WidgetLayer(
                                    widget: Image.file(
                                      File(val.params.url ?? ''),
                                    ),
                                  ),
                                );
                              } else {
                                // context.read<StickerBloc>().add(
                                //   StickerUploadFile(
                                //     [File(val.params.url ?? '')],
                                //     val.params,
                                //     val.type,
                                //   ),
                                // );
                              }
                            }
                          }
                        });
                      }
                            },
                      icon: vImage == null
                          ? EZResources.image(
                              ImageParams(name: AppIcons.icGreySend),
                            )
                          : EZResources.image(
                              ImageParams(name: AppIcons.icGreenSend),
                            ),
                    );
                  },
                ),
              ),
              Expanded(
                child: Galarey(
                  imageSelected: imageSelected,
                  imageTickerCamera: imgDisplaySelected,
                ),
              ),
            ],
          ),
        ),
      );
    },
    );
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: const SizedBox(
                    width: 60,
                    height: 60,
                    child: Icon(Icons.add),
                  ),
                ),
              ),
              ...List.generate(vSticker.length, (final i) {
                final item = ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: SizedBox(
                    width: 80,
                    height: 80,
                    child: Image.memory(base64Decode(vSticker[i])),
                  ),
                );
                return GestureDetector(
                  onTap: () {
                    setLayer(WidgetLayer(widget: item));
                    // context.read<StickerBloc>().add(
                    //   StickerRecentUpdate(
                    //     StickerUpdateRequestParams(
                    //       id: vSticker[i].id,
                    //       setId: vSticker[i].setId,
                    //     ),
                    //     type: StikerTypePop.selected,
                    //   ),
                    // );
                  },
                  child: item,
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeaderMedia(final String title, {final Widget? action}) {
    return ColoredBox(
      color: const Color(0xffF6F7FB),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: IconButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
              ),
            ),
            Align(
              child: Text(title, style: Theme.of(context).textTheme.titleSmall),
            ),
            if (action != null)
              Align(alignment: Alignment.centerRight, child: action),
          ],
        ),
      ),
    );
  }

  Padding _buildItemSet(
    final StickerSetSocialItems item,
    final BuildContext context,
    final Function(WidgetLayer) setLayer,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(item.name ?? ''),
            if ((item.stickers ?? []).isNotEmpty)
              Wrap(
                runAlignment: WrapAlignment.center,
                children: [
                  ...List.generate(item.stickers!.length, (final i2) {
                    final item2 = item.stickers?[i2];
                    final sticker = ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: SizedBox(
                        width: 60,
                        height: 60,
                        child: CachedNetworkImage(
                          imageUrl: item2?.link ?? '',
                          placeholder: (final context, final url) =>
                              const LoadingWidget(),
                          errorWidget:
                              (final context, final url, final error) =>
                                  const Icon(Icons.error),
                        ),
                      ),
                    );
                    return Padding(
                      padding: const EdgeInsets.all(6.0),
                      child: GestureDetector(
                        onTap: () async {
                          try {
                            // Get the image URL from item2?.link
                            final imageUrl = item2?.link ?? '';
                            if (imageUrl.isNotEmpty) {
                              // Download the image and convert to base64
                              final response = await http.get(
                                Uri.parse(imageUrl),
                              );
                              if (response.statusCode == 200) {
                                // Convert image bytes to base64
                                final base64String = base64Encode(
                                  response.bodyBytes,
                                );

                                // Add to recent stickers
                                stickersRecent.add(base64String);

                                // Save to cache
                                await EZCache.shared.saveStickerSocialRecent(
                                  stickersRecent.value,
                                );
                              }
                            }
                          } catch (e) {
                            debugPrint(
                              'Error converting sticker to base64: $e',
                            );
                          }

                          // Add layer to the image editor
                          setLayer(WidgetLayer(widget: sticker));
                        },
                        child: sticker,
                      ),
                    );
                  }),
                ],
              )
            else
              const SizedBox(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(final BuildContext context, final String title) {
    return ColoredBox(
      color: const Color(0xffF6F7FB),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: IconButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
              ),
            ),
            Align(
              child: Text(title, style: Theme.of(context).textTheme.titleSmall),
            ),
          ],
        ),
      ),
    );
  }
  

  Future<dynamic> _showDropSticker(
    final BuildContext context,
    final ValueNotifier<File?> imageFile,
    final CustomImageCropController controller,
  ) {
    return showModal(
      context: context,
      builder: (final context) => AlertDialog(
        actionsAlignment: MainAxisAlignment.center,
        titlePadding: EdgeInsets.zero,
        insetPadding: EdgeInsets.zero,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        title: _buildHeader(context, context.l10n.createStickers),
        content: SizedBox(
          width: MediaQuery.sizeOf(context).width,
          height: MediaQuery.sizeOf(context).height / 3,
          child: Stack(
            children: [
              ValueListenableBuilder(
                valueListenable: imageFile,
                builder: (final context, final vImageFile, final child) {
                  return vImageFile == null
                      ? const SizedBox()
                      : Stack(
                          children: [
                            Positioned.fill(
                              child: DottedBorder(
                                color: Theme.of(context).primaryColor,
                                dashPattern: const [3, 9],
                                strokeWidth: 4,
                                strokeCap: StrokeCap.round,
                                borderType: BorderType.RRect,
                                radius: const Radius.circular(5),
                                child: const SizedBox(),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(2.0),
                              child: Snappable(
                                key: _snappableKey,
                                child: CustomImageCrop(
                                  borderRadius: 5,
                                  cropPercentage: 1.24,
                                  outlineColor: Colors.white.withValues(
                                    alpha: 0,
                                  ),
                                  shape: CustomCropShape.Square,
                                  cropController: controller,
                                  image: FileImage(vImageFile, scale: .9),
                                ),
                              ),
                            ),
                          ],
                        );
                },
              ),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              final state = _snappableKey.currentState!;
              if (state.isGone) {
                state.reset();
              } else {
                state.snap();
              }
            },
            child: const Text('snap'),
          ),
          const SizedBox(height: 4),
          ElevatedButton(
            onPressed: () {
              final ValueNotifier<bool> isOpen = ValueNotifier(false);
              _openSticker(context, isOpen, controller).then((final val) {
                final isStickerType = val != null && val is StickerSave;
                if (isStickerType) {
                  if (context.mounted) {
                    if (val.type == StikerTypePop.selected) {
                      controller.addTransition(CropImageData());
                      controller.onCropImage().then((final data) async {
                        final tempDir = await getTemporaryDirectory();
                        final time = DateTime.now().formatDate3();
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(data?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.selected,
                                  ),
                                );
                              }
                            });
                      });
                    }
                    if (val.type == StikerTypePop.saveToLike) {
                      controller.addTransition(CropImageData());
                      controller.onCropImage().then((final data) async {
                        final tempDir = await getTemporaryDirectory();
                        final time = DateTime.now().formatDate3();
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(data?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.saveToLike,
                                  ),
                                );
                              }
                            });
                      });
                    }
                    if (val.type == StikerTypePop.saveToSet) {
                      controller.addTransition(CropImageData());
                      controller.onCropImage().then((final data) async {
                        final tempDir = await getTemporaryDirectory();
                        final time = DateTime.now().formatDate3();
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(data?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.saveToSet,
                                  ),
                                );
                              }
                            });
                      });
                    }
                  }
                }
              });
            },
            child: const Text('snap'),
          ),
        ],
      ),
    );
  }

  Future<dynamic> _openSticker(
    final BuildContext context,
    final ValueNotifier<bool> isOpen,
    final CustomImageCropController controller,
  ) async {
    final MemoryImage? bytes = await controller.onCropImage();
    if (context.mounted) {
      return showModal(
        context: context,
        builder: (final builder) {
          return GestureDetector(
            onTap: () {
              context.router.popForced();
            },
            child: AlertDialog.adaptive(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(0),
              ),
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.zero,
              contentPadding: EdgeInsets.zero,
              clipBehavior: Clip.antiAliasWithSaveLayer,
              content: SizedBox.expand(
                // child:
                //  Center(
                //   child: Column(
                //     mainAxisSize: MainAxisSize.min,
                //     mainAxisAlignment: MainAxisAlignment.center,
                //     children: [
                //       Container(
                //         decoration: BoxDecoration(
                //           borderRadius: BorderRadius.circular(8),
                //         ),
                //         width: MediaQuery.sizeOf(context).width / 2,
                //         height: MediaQuery.sizeOf(context).width / 2,
                //         child: Image.memory(bytes?.bytes ?? Uint8List(0)),
                //       ),
                //       const SizedBox(height: 12),
                //       Padding(
                //         padding: const EdgeInsets.symmetric(horizontal: 12),
                //         child: Stack(
                //           children: [
                //             SizedBox(
                //               child: Column(
                //                 children: [
                //                   DecoratedBox(
                //                     decoration: BoxDecoration(
                //                       color: Colors.white,
                //                       borderRadius: BorderRadius.circular(8),
                //                     ),
                //                     child: Column(
                //                       crossAxisAlignment:
                //                           CrossAxisAlignment.start,
                //                       mainAxisSize: MainAxisSize.min,
                //                       children: [
                //                         _buildSelectedSticker(context),
                //                         _buildFavoriteSticker(context),
                //                         _buildSaveSetSticker(isOpen),
                //                       ],
                //                     ),
                //                   ),
                //                   const SizedBox(height: 150),
                //                 ],
                //               ),
                //             ),
                //             ValueListenableBuilder(
                //               valueListenable: isOpen,
                //               builder:
                //                   (final context, final vIsOpen, final child) {
                //                     return AnimatedSize(
                //                       duration: const Duration(
                //                         milliseconds: 200,
                //                       ),
                //                       child: vIsOpen
                //                           ? _buildNewSet(isOpen, context)
                //                           : const SizedBox(),
                //                     );
                //                   },
                //             ),
                //           ],
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
             
              ),
            ).asGlass(),
          );
        },
      );
    }
  }

  ColoredBox _buildNewSet(
    final ValueNotifier<bool> isOpen,
    final BuildContext context,
  ) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
            IconButton(
              onPressed: () async {
                isOpen.value = false;
              },
              icon: EZResources.image(ImageParams(name: AppIcons.icBack)),
            ),
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('them muc moi'),
              onTap: () {
                final TextEditingController name = TextEditingController(
                  text: '',
                );
                showModal(
                  context: context,
                  builder: (final builder) {
                    return AlertDialog.adaptive(
                      title: const Text('tesst tao sticker'),
                      content: AccountField(
                        controller: name,
                        isOnlyReady: false,
                        label: '',
                        hintText: '',
                      ),
                      actions: [
                        TextButton(onPressed: () {}, child: const Text('huy')),
                        TextButton(
                          onPressed: () {
                            context.router.popForced();
                            context.router.popForced(
                              StickerSave(
                                StickerRequestParams(
                                  setName: name.text,
                                  label: 'tesstSet',
                                  url: '',
                                ),
                                StikerTypePop.saveToSet,
                              ),
                            );
                          },
                          child: const Text('ok'),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            SizedBox(
              width: MediaQuery.sizeOf(context).width / 2,
              height: 120,
              child: ListView.separated(
                itemBuilder: (final _, final i) {
                  final item = sets.value[i];
                  return ListTile(
                    onTap: () {
                      context.router.popForced(
                        StickerSave(
                          StickerRequestParams(
                            setId: item.id,
                            label: 'tesstSet',
                            url: '',
                          ),
                          StikerTypePop.saveToSet,
                        ),
                      );
                      // context.router.popForced(
                      //   _StickerSave(
                      //   StickerRequestParams(
                      //       label: 'tesstt123',
                      //       url: '',
                      //       setId: item.id,
                      //     ),
                      //     StikerTypePop.saveToSet,
                      // ),);
                    },
                    title: Text(item.name ?? ''),
                  );
                },
                separatorBuilder: (final _, final i) => const Divider(),
                itemCount: sets.value.length,
              ),
            ),
          ],
        ),
      );
    
  }

  ListTile _buildSaveSetSticker(final ValueNotifier<bool> isOpen) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('lưu vào mục cua ban'),
      onTap: () {
        isOpen.value = true;
      },
    );
  }

  ListTile _buildFavoriteSticker(final BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('lưu vào mục yêu thích'),
      onTap: () {
        context.router.popForced(
          StickerSave(
            const StickerRequestParams(
              setId: 'FAVORITE',
              label: 'tesst12',
              url: '',
            ),
            StikerTypePop.saveToLike,
          ),
        );
      },
    );
  }

  ListTile _buildSelectedSticker(final BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('chọn luôn'),
      onTap: () {
        context.router.popForced(
          StickerSave(
            const StickerRequestParams(
              setId: 'ANONYMOUS',
              label: 'tesst',
              url: '',
            ),
            StikerTypePop.selected,
          ),
        );
      },
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });
  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => max(maxHeight, minHeight);

  @override
  Widget build(
    final BuildContext context,
    final double shrinkOffset,
    final bool overlapsContent,
  ) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(final _SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
